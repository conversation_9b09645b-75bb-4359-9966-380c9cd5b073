<template>
  <div class="app-container">
    <!-- 步骤条 -->
    <div class="steps-container">
      <el-steps :active="3" finish-status="success" align-center>
        <el-step title="填写课程基本信息" />
        <el-step title="创建课程大纲" />
        <el-step title="发布课程" />
      </el-steps>
    </div>

    <!-- 课程发布信息 -->
    <div class="publish-container">
      <div class="publish-header">
        <h3>
          <i class="el-icon-upload" />
          课程发布确认
        </h3>
        <p>请确认课程信息无误后发布课程</p>
      </div>

      <div class="course-preview">
        <div class="course-cover-section">
          <div class="cover-wrapper">
            <img v-if="coursePublishInfo.cover" :src="coursePublishInfo.cover" class="course-cover" />
            <div v-else class="no-cover">
              <i class="el-icon-picture-outline" />
              <span>暂无封面</span>
            </div>
          </div>
        </div>

        <div class="course-info-section">
          <div class="course-title">
            {{ coursePublishInfo.title || '课程标题未设置' }}
          </div>

          <div class="course-meta">
            <div class="meta-item">
              <span class="meta-label">课程分类：</span>
              <span class="meta-value">
                {{ getSubjectDisplay() }}
              </span>
            </div>
            <div class="meta-item">
              <span class="meta-label">课程讲师：</span>
              <span class="meta-value">{{ coursePublishInfo.teacherName || '未设置讲师' }}</span>
            </div>
            <div class="meta-item">
              <span class="meta-label">总课时数：</span>
              <span class="meta-value">
                {{ getLessonNumDisplay() }}
              </span>
            </div>
            <div class="meta-item">
              <span class="meta-label">课程价格：</span>
              <span class="meta-value price">
                {{ getPriceDisplay() }}
              </span>
            </div>
          </div>

          <div class="course-description">
            <div class="description-title">课程简介</div>
            <div class="description-content" v-html="getDescriptionDisplay()"></div>
          </div>
        </div>
      </div>

      <div class="chapter-summary">
        <div class="summary-header">
          <h4>
            <i class="el-icon-menu" />
            课程大纲概览
          </h4>
          <div class="summary-stats">
            <span>{{ chapterCount }} 个章节</span>
            <span>{{ videoCount }} 个课时</span>
          </div>
        </div>

        <div class="chapter-list">
          <div
            v-for="(chapter, index) in chapterList"
            :key="chapter.id"
            class="chapter-summary-item">
            <div class="chapter-info">
              <span class="chapter-number">第{{ index + 1 }}章</span>
              <span class="chapter-title">{{ chapter.title || '未命名章节' }}</span>
              <span class="video-count">{{ chapter.children ? chapter.children.length : 0 }}个课时</span>
            </div>
          </div>

          <!-- 空状态显示 -->
          <div v-if="chapterList.length === 0" class="empty-chapter-state">
            <i class="el-icon-folder-opened"></i>
            <p>暂无课程大纲</p>
            <span>请先添加章节和课时</span>
          </div>
        </div>
      </div>

      <!-- 调试信息面板（开发环境） -->
      <div v-if="showDebugInfo" class="debug-panel">
        <div class="debug-header">
          <h4>调试信息</h4>
          <el-button type="text" size="mini" @click="showDebugInfo = false">隐藏</el-button>
        </div>
        <div class="debug-content">
          <h5>课程信息:</h5>
          <pre>{{ JSON.stringify(coursePublishInfo, null, 2) }}</pre>
          <h5>章节列表:</h5>
          <pre>{{ JSON.stringify(chapterList, null, 2) }}</pre>
        </div>
      </div>

      <div class="publish-actions">
        <el-button
          size="large"
          icon="el-icon-back"
          @click="previous">
          上一步
        </el-button>
        <el-button
          v-if="!showDebugInfo"
          type="info"
          size="small"
          @click="showDebugInfo = true">
          显示调试信息
        </el-button>
        <el-button
          :loading="publishLoading"
          type="success"
          size="large"
          icon="el-icon-upload"
          @click="publishCourse">
          发布课程
        </el-button>
      </div>
    </div>
  </div>
</template>

<script>
import course from '@/api/edu/course'
import chapter from '@/api/edu/chapter'

export default {
  data() {
    return {
      courseId: '',
      coursePublishInfo: {},
      chapterList: [],
      publishLoading: false,
      showDebugInfo: false
    }
  },
  computed: {
    chapterCount() {
      return this.chapterList.length
    },
    videoCount() {
      let total = 0
      this.chapterList.forEach(chapter => {
        if (chapter.children) {
          total += chapter.children.length
        }
      })
      return total
    }
  },
  created() {
    if (this.$route.params && this.$route.params.id) {
      this.courseId = this.$route.params.id
      console.log('页面初始化，课程ID:', this.courseId)

      // 先获取章节信息，再获取课程信息
      this.getChapterList().then(() => {
        this.getCoursePublishInfo()
      })
    } else {
      console.error('未获取到课程ID')
      this.$message.error('课程ID缺失')
      this.$router.push({ path: '/course/list' })
    }
  },
  methods: {
    getCoursePublishInfo() {
      console.log('获取课程发布信息，课程ID:', this.courseId)
      course.getCoursePublishInfo(this.courseId)
        .then(response => {
          console.log('课程发布信息响应:', response)

          // 处理不同的响应数据结构
          if (response.data) {
            if (response.data.coursePublishInfo) {
              this.coursePublishInfo = response.data.coursePublishInfo
            } else if (response.data.data) {
              this.coursePublishInfo = response.data.data
            } else {
              this.coursePublishInfo = response.data
            }
          } else {
            this.coursePublishInfo = response
          }

          console.log('处理后的课程信息:', this.coursePublishInfo)

          // 如果没有获取到数据，尝试使用基本课程信息
          if (!this.coursePublishInfo || Object.keys(this.coursePublishInfo).length === 0) {
            console.log('课程发布信息为空，尝试获取基本课程信息')
            this.getCourseBasicInfo()
          } else {
            console.log('课程发布信息获取成功')
          }
        })
        .catch(error => {
          console.log('获取课程发布信息失败:', error)
          console.log('错误详情:', error.response || error.message)

          // 如果是404错误，说明接口不存在，直接使用基本信息
          if (error.response && error.response.status === 404) {
            console.log('发布信息接口不存在，使用基本课程信息')
            this.getCourseBasicInfo()
          } else {
            this.$message.warning('获取课程发布信息失败，使用基本信息')
            this.getCourseBasicInfo()
          }
        })
    },

    // 获取基本课程信息作为备选方案
    getCourseBasicInfo() {
      console.log('获取基本课程信息，课程ID:', this.courseId)
      course.getCourseInfoById(this.courseId)
        .then(response => {
          console.log('基本课程信息响应:', response)

          if (response.data) {
            if (response.data.courseInfo) {
              this.coursePublishInfo = response.data.courseInfo
            } else if (response.data.data) {
              this.coursePublishInfo = response.data.data
            } else {
              this.coursePublishInfo = response.data
            }
          } else {
            this.coursePublishInfo = response
          }

          console.log('处理后的基本课程信息:', this.coursePublishInfo)

          // 确保有默认值
          if (!this.coursePublishInfo.lessonNum) {
            this.coursePublishInfo.lessonNum = this.videoCount
          }
        })
        .catch(error => {
          console.log('获取基本课程信息也失败:', error)
          this.$message.error('获取课程信息失败')

          // 设置默认的空对象，避免页面报错
          this.coursePublishInfo = {
            title: '课程标题获取失败',
            price: 0,
            lessonNum: 0,
            description: '',
            cover: '',
            teacherName: '',
            subjectParentTitle: '',
            subjectTitle: ''
          }
        })
    },
    getChapterList() {
      console.log('获取章节列表，课程ID:', this.courseId)
      return chapter.getChapterList(this.courseId)
        .then(response => {
          console.log('章节列表响应:', response)

          // 处理您的实际数据结构
          if (response.data && response.data.allChapterVideo) {
            this.chapterList = response.data.allChapterVideo
          } else if (response.data && response.data.list) {
            this.chapterList = response.data.list
          } else if (response.data && response.data.data) {
            this.chapterList = response.data.data
          } else if (response.data && Array.isArray(response.data)) {
            this.chapterList = response.data
          } else if (Array.isArray(response)) {
            this.chapterList = response
          } else {
            this.chapterList = []
          }

          console.log('处理后的章节列表:', this.chapterList)
          console.log('章节数量:', this.chapterList.length)
          console.log('总课时数:', this.videoCount)

          return this.chapterList
        })
        .catch(error => {
          console.log('获取章节列表失败:', error)
          this.$message.error('获取课程大纲失败')
          this.chapterList = []
          return []
        })
    },
    publishCourse() {
      this.$confirm('确定要发布这门课程吗？发布后学员就可以看到该课程', '发布确认', {
        confirmButtonText: '确定发布',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.publishLoading = true
        course.publishCourse(this.courseId)
          .then(response => {
            this.$message({
              type: 'success',
              message: '课程发布成功！',
              duration: 3000
            })
            this.$router.push({ path: '/course/list' })
          })
          .catch(error => {
            console.log(error)
            this.$message.error('课程发布失败')
          })
          .finally(() => {
            this.publishLoading = false
          })
      })
    },
    previous() {
      this.$router.push({ path: `/course/chapter/${this.courseId}` })
    },

    // 获取课程分类显示文本
    getSubjectDisplay() {
      const parent = this.coursePublishInfo.subjectParentTitle || this.coursePublishInfo.subjectParent
      const child = this.coursePublishInfo.subjectTitle || this.coursePublishInfo.subject

      if (parent && child) {
        return `${parent} / ${child}`
      } else if (parent) {
        return parent
      } else if (child) {
        return child
      } else {
        return '未设置分类'
      }
    },

    // 获取课时数显示文本
    getLessonNumDisplay() {
      // 优先使用实际统计的课时数，然后使用课程信息中的课时数
      const actualVideoCount = this.videoCount
      const courseInfoLessonNum = this.coursePublishInfo.lessonNum

      const lessonNum = actualVideoCount > 0 ? actualVideoCount : (courseInfoLessonNum || 0)

      console.log('课时数计算:', {
        actualVideoCount,
        courseInfoLessonNum,
        finalLessonNum: lessonNum
      })

      return `${lessonNum} 课时`
    },

    // 获取价格显示文本
    getPriceDisplay() {
      const price = this.coursePublishInfo.price
      if (price !== undefined && price !== null) {
        return `￥${price}`
      } else {
        return '￥0'
      }
    },

    // 获取课程简介显示文本
    getDescriptionDisplay() {
      const description = this.coursePublishInfo.description
      if (description && description.trim()) {
        return description
      } else {
        return '<p style="color: #999; font-style: italic;">暂无课程简介</p>'
      }
    },

    // 测试所有可能的课程信息接口
    testAllCourseInfoApis() {
      console.log('=== 测试所有课程信息接口 ===')

      // 测试发布信息接口
      console.log('1. 测试发布信息接口...')
      course.getCoursePublishInfo(this.courseId)
        .then(response => {
          console.log('发布信息接口成功:', response)
        })
        .catch(error => {
          console.log('发布信息接口失败:', error.response?.status, error.message)
        })

      // 测试基本信息接口
      console.log('2. 测试基本信息接口...')
      course.getCourseInfoById(this.courseId)
        .then(response => {
          console.log('基本信息接口成功:', response)
        })
        .catch(error => {
          console.log('基本信息接口失败:', error.response?.status, error.message)
        })

      // 测试另一个可能的接口
      console.log('3. 测试另一个课程信息接口...')
      course.getCourseInfo(this.courseId)
        .then(response => {
          console.log('课程信息接口成功:', response)
        })
        .catch(error => {
          console.log('课程信息接口失败:', error.response?.status, error.message)
        })
    }
  }
}
</script>

<style scoped>
/* 步骤条样式 */
.steps-container {
  background: white;
  border-radius: 12px;
  padding: 30px;
  margin-bottom: 24px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

/* 发布容器样式 */
.publish-container {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.publish-header {
  background: #f8f9fa;
  padding: 24px;
  border-bottom: 1px solid #e9ecef;
  text-align: center;
}

.publish-header h3 {
  margin: 0 0 8px 0;
  font-size: 20px;
  font-weight: 600;
  color: #2c3e50;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
}

.publish-header i {
  color: #67c23a;
  font-size: 24px;
}

.publish-header p {
  margin: 0;
  color: #666;
  font-size: 14px;
}

/* 课程预览样式 */
.course-preview {
  padding: 32px;
  display: flex;
  gap: 32px;
}

.course-cover-section {
  flex-shrink: 0;
}

.cover-wrapper {
  width: 300px;
  height: 200px;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.course-cover {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.no-cover {
  width: 100%;
  height: 100%;
  background: #f5f5f5;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #999;
}

.no-cover i {
  font-size: 48px;
  margin-bottom: 8px;
}

.course-info-section {
  flex: 1;
}

.course-title {
  font-size: 28px;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 24px;
  line-height: 1.3;
}

.course-meta {
  margin-bottom: 24px;
}

.meta-item {
  display: flex;
  margin-bottom: 12px;
  align-items: center;
}

.meta-label {
  font-weight: 500;
  color: #666;
  width: 100px;
  flex-shrink: 0;
}

.meta-value {
  color: #2c3e50;
}

.meta-value.price {
  color: #f56c6c;
  font-size: 18px;
  font-weight: 600;
}

.course-description {
  border-top: 1px solid #e9ecef;
  padding-top: 24px;
}

.description-title {
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 12px;
}

.description-content {
  color: #666;
  line-height: 1.6;
  max-height: 120px;
  overflow-y: auto;
}

/* 章节概览样式 */
.chapter-summary {
  border-top: 1px solid #e9ecef;
  padding: 32px;
}

.summary-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.summary-header h4 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
  display: flex;
  align-items: center;
  gap: 12px;
}

.summary-header i {
  color: #409eff;
  font-size: 20px;
}

.summary-stats {
  display: flex;
  gap: 16px;
  color: #666;
  font-size: 14px;
}

.chapter-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 16px;
}

.chapter-summary-item {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 16px;
  border-left: 4px solid #409eff;
}

.chapter-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.chapter-number {
  background: #409eff;
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
  flex-shrink: 0;
}

.chapter-title {
  font-weight: 500;
  color: #2c3e50;
  flex: 1;
}

.video-count {
  color: #666;
  font-size: 12px;
  flex-shrink: 0;
}

/* 空状态样式 */
.empty-chapter-state {
  text-align: center;
  padding: 40px 20px;
  color: #999;
  background: #fafafa;
  border-radius: 8px;
  border: 2px dashed #e4e7ed;
}

.empty-chapter-state i {
  font-size: 48px;
  margin-bottom: 12px;
  display: block;
  color: #ddd;
}

.empty-chapter-state p {
  font-size: 16px;
  margin: 0 0 8px 0;
  color: #666;
}

.empty-chapter-state span {
  font-size: 14px;
  color: #999;
}

/* 调试面板样式 */
.debug-panel {
  border-top: 1px solid #e9ecef;
  padding: 20px 32px;
  background: #f8f9fa;
}

.debug-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.debug-header h4 {
  margin: 0;
  color: #666;
  font-size: 14px;
}

.debug-content h5 {
  margin: 16px 0 8px 0;
  color: #666;
  font-size: 13px;
}

.debug-content pre {
  background: white;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 12px;
  font-size: 12px;
  max-height: 200px;
  overflow-y: auto;
  margin: 0 0 16px 0;
}

/* 发布操作样式 */
.publish-actions {
  border-top: 1px solid #e9ecef;
  padding: 24px 32px;
  display: flex;
  justify-content: center;
  gap: 16px;
}

.publish-actions .el-button {
  min-width: 120px;
  height: 44px;
  border-radius: 22px;
  font-weight: 500;
}

.publish-actions .el-button--success {
  background: linear-gradient(135deg, #67c23a 0%, #85ce61 100%);
  border: none;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .course-preview {
    flex-direction: column;
    padding: 20px;
  }
  
  .cover-wrapper {
    width: 100%;
    max-width: 300px;
    margin: 0 auto;
  }
  
  .chapter-list {
    grid-template-columns: 1fr;
  }
  
  .chapter-summary {
    padding: 20px;
  }
  
  .publish-actions {
    flex-direction: column;
    padding: 20px;
  }
  
  .publish-actions .el-button {
    width: 100%;
  }
}

/* 动画效果 */
.publish-container {
  animation: slideInUp 0.5s ease-out;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
