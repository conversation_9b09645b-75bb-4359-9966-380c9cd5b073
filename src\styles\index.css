/* Global styles for vue-admin-template */

body {
  height: 100%;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  text-rendering: optimizeLegibility;
  font-family: Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB, Microsoft YaHei, Arial, sans-serif;
}

label {
  font-weight: 700;
}

html {
  height: 100%;
  box-sizing: border-box;
}

#app{
  height: 100%;
}

*,
*:before,
*:after {
  box-sizing: inherit;
}

a,
a:focus,
a:hover {
  cursor: pointer;
  color: inherit;
  outline: none;
  text-decoration: none;
}

div:focus{
  outline: none;
}

a:focus,
a:active {
  outline: none;
}

.clearfix:after {
  visibility: hidden;
  display: block;
  font-size: 0;
  content: " ";
  clear: both;
  height: 0;
}

/* main-container全局样式 */
.app-main{
  min-height: 100%
}

.app-container {
  padding: 20px;
}

/* Sidebar styles */
#app .main-container {
  min-height: 100%;
  transition: margin-left .28s;
  margin-left: 180px;
  position: relative;
}

#app .sidebar-container {
  transition: width 0.28s;
  width: 180px !important;
  height: 100%;
  position: fixed;
  font-size: 0px;
  top: 0;
  bottom: 0;
  left: 0;
  z-index: 1001;
  overflow: hidden;
}

#app .sidebar-container .horizontal-collapse-transition {
  transition: 0s width ease-in-out, 0s padding-left ease-in-out, 0s padding-right ease-in-out;
}

#app .sidebar-container .el-scrollbar__bar.is-vertical{
  right: 0px;
}

#app .sidebar-container .scrollbar-wrapper {
  overflow-x: hidden!important;
}

#app .sidebar-container .scrollbar-wrapper .el-scrollbar__view {
  height: 100%;
}

#app .sidebar-container .is-horizontal {
  display: none;
}

#app .sidebar-container a {
  display: inline-block;
  width: 100%;
  overflow: hidden;
}

#app .sidebar-container .svg-icon {
  margin-right: 16px;
}

#app .sidebar-container .el-menu {
  border: none;
  height: 100%;
  width: 100% !important;
}

#app .sidebar-container .is-active > .el-submenu__title{
  color: #f4f4f5!important;
}

#app .hideSidebar .sidebar-container {
  width: 36px !important;
}

#app .hideSidebar .main-container {
  margin-left: 36px;
}

#app .hideSidebar .submenu-title-noDropdown {
  padding-left: 10px !important;
  position: relative;
}

#app .hideSidebar .submenu-title-noDropdown .el-tooltip {
  padding: 0 10px !important;
}

#app .hideSidebar .el-submenu {
  overflow: hidden;
}

#app .hideSidebar .el-submenu > .el-submenu__title {
  padding-left: 10px !important;
}

#app .hideSidebar .el-submenu > .el-submenu__title .el-submenu__icon-arrow {
  display: none;
}

#app .hideSidebar .el-menu--collapse .el-submenu > .el-submenu__title > span {
  height: 0;
  width: 0;
  overflow: hidden;
  visibility: hidden;
  display: inline-block;
}

#app .sidebar-container .nest-menu .el-submenu > .el-submenu__title,
#app .sidebar-container .el-submenu .el-menu-item {
  min-width: 180px !important;
  background-color: #1f2d3d !important;
}

#app .sidebar-container .nest-menu .el-submenu > .el-submenu__title:hover,
#app .sidebar-container .el-submenu .el-menu-item:hover {
  background-color: #001528 !important;
}

#app .el-menu--collapse .el-menu .el-submenu {
  min-width: 180px !important;
}

/* Mobile styles */
#app.mobile .main-container {
  margin-left: 0px;
}

#app.mobile .sidebar-container {
  transition: transform .28s;
  width: 180px !important;
}

#app.mobile.hideSidebar .sidebar-container {
  transition-duration: 0.3s;
  transform: translate3d(-180px, 0, 0);
}

#app .withoutAnimation .main-container,
#app .withoutAnimation .sidebar-container {
  transition: none;
}

.el-menu--vertical > .el-menu .svg-icon{
  margin-right: 16px;
}

/* Element UI overrides */
.el-upload input[type="file"] {
  display: none !important;
}

.el-upload__input {
  display: none;
}

.el-dialog {
  transform: none;
  left: 0;
  position: relative;
  margin: 0 auto;
}

.upload-container .el-upload {
  width: 100%;
}

.upload-container .el-upload .el-upload-dragger {
  width: 100%;
  height: 200px;
}

/* Transition styles */
.fade-enter-active, .fade-leave-active {
  transition: opacity 0.28s;
}

.fade-enter, .fade-leave-active {
  opacity: 0;
}

.fade-transform-leave-active,
.fade-transform-enter-active {
  transition: all .5s;
}

.fade-transform-enter {
  opacity: 0;
  transform: translateX(-30px);
}

.fade-transform-leave-to {
  opacity: 0;
  transform: translateX(30px);
}

.breadcrumb-enter-active, .breadcrumb-leave-active {
  transition: all .5s;
}

.breadcrumb-enter, .breadcrumb-leave-active {
  opacity: 0;
  transform: translateX(20px);
}

.breadcrumb-move {
  transition: all .5s;
}

.breadcrumb-leave-active {
  position: absolute;
}
